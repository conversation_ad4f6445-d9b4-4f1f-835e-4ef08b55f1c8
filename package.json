{"name": "prepaid-meter-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@capacitor/android": "^7.2.0", "@capacitor/cli": "^7.2.0", "@capacitor/core": "^7.2.0", "chart.js": "^4.4.0", "date-fns": "^2.30.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-icons": "^4.12.0", "react-router-dom": "^6.20.1"}, "devDependencies": {"@capacitor/assets": "^3.0.5", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "vite": "^5.4.10"}}