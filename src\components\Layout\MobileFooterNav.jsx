import React from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { useTheme } from '../../context/ThemeContext'
import { HiHome, HiCurrencyDollar, HiTrendingUp, HiClipboardList } from 'react-icons/hi'

function MobileFooterNav() {
  const location = useLocation()
  const navigate = useNavigate()
  const { theme } = useTheme()

  const navItems = [
    {
      id: 'dashboard',
      path: '/',
      icon: HiHome,
      label: 'Dashboard'
    },
    {
      id: 'purchases',
      path: '/purchases',
      icon: HiCurrencyDollar,
      label: 'Purchases'
    },
    {
      id: 'usage',
      path: '/usage',
      icon: HiTrendingUp,
      label: 'Usage'
    },
    {
      id: 'history',
      path: '/history',
      icon: HiClipboardList,
      label: 'History'
    }
  ]

  const isActive = (path) => {
    if (path === '/') {
      return location.pathname === '/' || location.pathname === '/dashboard'
    }
    return location.pathname === path
  }

  return (
    <div className={`md:hidden fixed bottom-0 left-0 right-0 z-30 ${theme.card} border-t ${theme.border} backdrop-blur-lg`}>
      <div className="flex items-center justify-around px-2 py-2">
        {navItems.map((item) => {
          const Icon = item.icon
          const active = isActive(item.path)
          
          return (
            <button
              key={item.id}
              onClick={() => navigate(item.path)}
              className={`flex flex-col items-center justify-center p-2 rounded-lg transition-all duration-200 min-w-0 flex-1 ${
                active 
                  ? `${theme.primary} text-white shadow-lg transform scale-105` 
                  : `${theme.text} hover:${theme.secondary}`
              }`}
            >
              <Icon className={`h-5 w-5 ${active ? 'text-white' : theme.textSecondary}`} />
              <span className={`text-xs font-medium mt-1 truncate ${
                active ? 'text-white' : theme.textSecondary
              }`}>
                {item.label}
              </span>
            </button>
          )
        })}
      </div>
    </div>
  )
}

export default MobileFooterNav
